import logging
import asyncio
import random
import signal
import sys
from playwright.async_api import <PERSON><PERSON>,Page
from utils.config_manager import ConfigManager
import time
from utils.mail_verification_helper import MailVerificationHelper

class MicrosoftRewardMobileTask:
    """
    Microsoft Rewards 奖励任务
    """
    def __init__(
        self,
        config_manager: ConfigManager
    ) -> None:
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager


    async def handler_single_account(self, page: Page, email: str, password: str, index: int = 0, max_retries: int = 3):
        """执行奖励任务"""
        retry_count = 0
        while retry_count < max_retries:
            try:
                self.logger.info(f"开始执行奖励任务 [{index}]: {email} (尝试 {retry_count + 1})")
                
                # 清除所有cookies
                await page.context.clear_cookies()

                # 访问奖励页面
                await page.goto("https://rewards.bing.com")
                # 输入邮箱
                await page.wait_for_url('https://login.live.com/oauth20_authorize.srf**', timeout=3000)
                await page.fill('input[type="email"]', email)
                await page.click('button[type="submit"]')
      

                try:
                    await page.wait_for_selector('#idA_PWD_SwitchToPassword', timeout=3000)
                    await page.click('#idA_PWD_SwitchToPassword')
                except Exception as e:
                    self.logger.info(f"没有处理多重验证，继续执行: {str(e)}")
          
                # 输入密码
                await page.fill('input[type="password"]', password)
                await page.fill('input[type="password"]', password)
                await page.click('button[type="submit"]')

                # 密码错误,可能未修改
                try:
                    await page.wait_for_url('https://login.live.com/ppsecure/**', timeout=3000)
                    #await page.wait_for_selector("#i0118Error",timeout=3000)
                    await page.wait_for_selector("#field-8__validationMessage",timeout=5000)
                    raise Exception("密码错误")
                except Exception as e:
                    if "密码错误" in str(e):
                        retry_count = 1000
                        raise Exception("密码错误")
                    self.logger.info(f"密码正确，继续执行: {str(e)}")
                    
                # 等待手机验证
                service_abuseLanding = False
                try:
                    await page.wait_for_url(lambda url: url.startswith('https://account.live.com/Abuse'), timeout=5000)
                    service_abuseLanding = True
                except Exception as e:
                    self.logger.info(f"没有手机验证页面，继续执行: {str(e)}")
    
                if service_abuseLanding:
                    retry_count = max_retries
                    raise Exception("账号被锁定")
                    

                try:
                    await page.wait_for_url(lambda url: url.startswith('https://account.live.com/recover'), timeout=5000)
                    await page.click('input[type="submit"]')    
                    
                    
                    proof_email_config = self.config_manager.proof_email[0]
                    proof_domain = proof_email_config['domain']
                    proof_api = proof_email_config['api']
                    proof_email = f"{email.split('@')[0]}@{proof_domain}"
                    
                    await page.wait_for_selector("#iProofEmail", timeout=3000)
                    await page.fill("#iProofEmail", proof_email)
                    timestamp = int(time.time())
                    await page.click('input[type="submit"]')
                    # 获取验证码
                    verification_code = await MailVerificationHelper.get_ms_verification_code(proof_api,proof_email, timestamp)
                    await page.fill('input[type="tel"]', verification_code)
                    await page.click('input[type="submit"]')
                except Exception as e:
                    self.logger.info(f"没有帮助我们保护帐户页面，继续执行: {str(e)}")

                # 处理多重验证
                for i in range(2):
                    try:
                        await page.wait_for_url('https://account.live.com/identity/**', timeout=3000)
                        proof_email_config = self.config_manager.proof_email[0]
                        proof_domain = proof_email_config['domain']
                        proof_api = proof_email_config['api']
                        proof_email = f"{email.split('@')[0]}@{proof_domain}"
                        
                        await page.fill("#iProofEmail", proof_email)
                        timestamp = int(time.time())
                        await page.click('input[type="submit"]')
                        # 获取验证码
                        verification_code = await MailVerificationHelper.get_ms_verification_code(proof_api,proof_email, timestamp)
                        await page.fill('input[type="tel"]', verification_code)
                        await page.click('input[type="submit"]')
                    except Exception as e:
                        self.logger.info(f"没有多重验证，继续执行: {str(e)}")

          
                try:
                    await page.wait_for_url('https://account.live.com/interrupt/**', timeout=3000)
                    await page.click('div[data-testid="textButtonContainer"] > div:first-child > button[type="button"]')
                except Exception as e:
                    self.logger.info(f"无通行密钥软件提示，继续执行: {str(e)}")

                 # 确认登录
                try:
                    await page.wait_for_url('https://login.live.com/ppsecure/**', timeout=3000)
                    await page.click('button[type="submit"]')
                except Exception as e:
                    self.logger.info(f"无登录确认，继续执行: {str(e)}")

                # 确认登录有时出现
                try:
                    await page.wait_for_url('https://login.live.com/oauth20_authorize.srf**', timeout=3000)
                    await page.click('button[type="submit"]')
                except Exception as e:
                    self.logger.info(f"无登录确认，继续执行: {str(e)}")


                try:
                    await page.wait_for_url('https://login.live.com/oauth20_authorize.srf**', timeout=3000)
                    await page.click('button[type="submit"]')
                except Exception as e:
                    self.logger.info(f"无登录确认，继续执行: {str(e)}")

                try:
                    await page.wait_for_url(lambda url: url.startswith('https://rewards.bing.com'), timeout=5000)
                    await page.evaluate("""() => {
                        const cookieContainer = document.querySelector('#cookieConsentContainer');
                        if (cookieContainer) {
                            cookieContainer.style.display = 'none';
                        }
                    }""")
                    await page.wait_for_timeout(1000)
                except Exception as e:
                    self.logger.info(f"没有手机验证页面，继续执行: {str(e)}")

                # 等待页面加载完成
                try:
                    #await page.wait_for_url('https://rewards.bing.com/welcometour', timeout=3000)
                    await page.wait_for_selector('#welcome-tour', timeout=5000)
                    await page.click('#welcome-tour #fre-next-button:visible')
                    await asyncio.sleep(random.uniform(3, 5))
                    await page.click('#welcome-tour #fre-next-button:visible')
  
                    await page.wait_for_selector('#welcome-tour .interest-buttons:visible', timeout=3000)
                    # 获取所有符合条件的按钮
                    buttons = await page.locator("#welcome-tour .interest-button:visible").all()
                    if buttons:
                        # 随机选择一个按钮
                        selected_button = random.choice(buttons)
                        await selected_button.click()
                    else:
                        self.logger.info("未找到匹配的引导按钮")
                    await page.click('#welcome-tour #claim-button:visible')
                except Exception as e:
                    self.logger.info(f"没有引导页面，继续执行: {str(e)}")



                has_error = False
                try:
                    await page.wait_for_selector('#fraudErrorBody', timeout=3000)
                    has_error = True
                except Exception as e:
                    self.logger.info(f"没有错误，继续执行: {str(e)}")

                if has_error:
                    retry_count = max_retries
                    raise Exception("Fraud error detected")

                try:
                    await page.wait_for_selector('.maybe-later', timeout=5000)
                    await page.click('.maybe-later')
                except Exception as e:
                    self.logger.info(f"没有必应应用遮罩，继续执行: {str(e)}")


                await page.wait_for_selector("#daily-sets")
                modeOn = page.locator("#daily-sets #balanceToolTipDiv #ModeOn.toggleOff")
                if await modeOn.is_visible():
                    await page.click('#daily-sets #balanceToolTipDiv #ModeOn')

                await asyncio.sleep(random.uniform(3, 5))
           
                self.logger.info(f'完成初始化 {email}')
                break
                
            except Exception as e:
                retry_count += 1
                self.logger.error(f"奖励任务尝试 {retry_count} 失败 {email}: {str(e)}")
                
                if retry_count >= max_retries:
                    self.logger.error(f"达到最大重试次数 {email}")
                    raise Exception(f"Failed after {max_retries} attempts: {str(e)}")
                
                await asyncio.sleep(random.uniform(5, 10))
                continue
            
    async def do(self, playwright: Playwright, accounts: list, max_concurrent_tasks: int = 1):
        failed_accounts = []
        success_accounts = []
        
        sem = asyncio.Semaphore(max_concurrent_tasks)
        
        tasks = []
        # 获取当前事件循环
        loop = asyncio.get_running_loop()

        # 定义信号处理函数
        def handle_sigint():
            """处理Ctrl+C信号"""
            self.logger.info("\nReceived termination signal, cancelling tasks...")
            for task in tasks:
                if not task.done():
                    task.cancel()

        # 注册信号处理器（兼容Windows）
        if sys.platform == 'win32':
            signal.signal(signal.SIGINT, lambda s, f: loop.call_soon_threadsafe(handle_sigint))
        else:
            loop.add_signal_handler(signal.SIGINT, handle_sigint)
        
        async def process_account_group(account_group):
            async with sem:
                return await self.batch(playwright, account_group)
        
        accounts_per_group = len(accounts) // max_concurrent_tasks
        remainder = len(accounts) % max_concurrent_tasks

        account_groups = []
        start_idx = 0

        for i in range(max_concurrent_tasks):
            group_size = accounts_per_group + (1 if i < remainder else 0)
            account_groups.append(accounts[start_idx:start_idx + group_size])
            start_idx += group_size
        
        for account_group in account_groups:
            task = asyncio.create_task(process_account_group(account_group))
            tasks.append(task)
    
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, Exception):
                    self.logger.error(f"Task failed: {str(result)}")
                elif isinstance(result, tuple):  # 修改为接收元组
                    success, failed = result
                    success_accounts.extend(success)
                    failed_accounts.extend(failed)
                    
        except asyncio.CancelledError:
            self.logger.info("Main execution was cancelled")
        finally:
            if sys.platform != 'win32':
                loop.remove_signal_handler(signal.SIGINT)
    
        return success_accounts, failed_accounts

    async def batch(self, playwright: Playwright, accounts: list):
        """批量处理奖励任务"""
        failed_accounts = []
        success_accounts = []

        # Configure proxy settings if provided
        browser_options = {
            'headless': False,
            'args': ['--disable-web-security']
        }
        
        context_options = {
            'ignore_https_errors': True,
            'viewport':{'width': 375, 'height': 812},
            'is_mobile': True,
            'user_agent':'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15'
        }
        
        # Launch browser and create context
        browser = await playwright.chromium.launch(**browser_options)
        context = await browser.new_context(**context_options)
        page = await context.new_page()
        
        try:
            for index, account in enumerate(accounts, 1):
                try:
                    email = account['email']
                    password = account['password']
                    await self.handler_single_account(page, email, password, index)
                    success_accounts.append(account)
                    await asyncio.sleep(random.uniform(3, 5))
                except Exception as e:
                    self.logger.error(f"Account processing failed for [{index}] {email}: {str(e)}")
                    failed_accounts.append(account)
        finally:
            await page.close()
            await context.close()
            await browser.close()
            
        return success_accounts, failed_accounts
