import logging
import asyncio
import signal
import sys
import time
import random
from playwright.async_api import <PERSON><PERSON>, <PERSON>
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from utils.config_manager import ConfigManager
from utils.mail_verification_helper import MailVerificationHelper


class EmailError(Exception):
    """自定义的异常类"""
    def __init__(self, message="EmailError"):
        self.message = message
        super().__init__(self.message)
    def __str__(self):
        return f"EmailError: {self.message}"

class MicrosoftAccountResetTask:
    """
    邮箱登录任务
    """
    def __init__(
        self,
        config_manager: ConfigManager
    ) -> None:
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
       
  

    async def loginsrf(self,page: Page,proof_api:str,proof_email:str):
        
        # 处理多重验证
        try:
            await page.wait_for_url('https://account.live.com/proofs/**', timeout=3000)
            await page.fill("#EmailAddress", proof_email)
            timestamp = int(time.time())
            await page.click('input[type="submit"]')
            # 获取验证码
            verification_code = await MailVerificationHelper.get_ms_verification_code(
                proof_api,
                proof_email,
                timestamp
            )
            await page.fill('input[type="tel"]', verification_code)
            await page.click('input[type="submit"]')
        except Exception as e:
            self.logger.info(f"没有处理多重验证，继续执行: {str(e)}")
        
        try:
            await page.wait_for_url('https://login.live.com/login.srf**', timeout=3000)
           
            idDiv_SAOTCS_Proofs = page.locator("#idDiv_SAOTCS_Proofs")
            fui_CardHeader__header = page.locator(".fui-Card")

            if await fui_CardHeader__header.is_visible():
                await page.click('.fui-Card [role="button"]')
                await page.fill('#proof-confirmation-email-input', proof_email)
                timestamp = int(time.time())
                await page.click('button[type="submit"]')

                verification_code = await MailVerificationHelper.get_ms_verification_code(
                    proof_api,
                    proof_email, 
                    timestamp
                )

                for i, digit in enumerate(verification_code):
                    await page.fill(f'#codeEntry-{i}', digit)
                    
            elif await idDiv_SAOTCS_Proofs.is_visible():
                await page.click('#idDiv_SAOTCS_Proofs [role="button"]')
                await page.fill('#idTxtBx_SAOTCS_ProofConfirmation', proof_email)
                timestamp = int(time.time())
                await page.click('#idSubmit_SAOTCS_SendCode')

                verification_code = await MailVerificationHelper.get_ms_verification_code(
                    proof_api,
                    proof_email, 
                    timestamp
                )
                await page.fill('#idTxtBx_SAOTCC_OTC', verification_code)
                await page.click('#idSubmit_SAOTCC_Continue')
              
            else:
                await page.click('button[type="button"]')
                await page.fill('#proof-confirmation', proof_email)
                timestamp = int(time.time())
                await page.click('button[type="submit"]')
                
                try:
                    await page.wait_for_selector("#proof-confirmationError",timeout=3000)
                    raise EmailError("email被绑定到了其他邮箱")
                except PlaywrightTimeoutError as e:
                    self.logger.info(f"邮箱正确，继续执行: {str(e)}")
                    
                verification_code = await MailVerificationHelper.get_ms_verification_code(
                    proof_api, 
                    proof_email,
                    timestamp
                )
                await page.fill('#otc-confirmation-input', verification_code)
                await page.click('button[type="submit"]')
             
        except PlaywrightTimeoutError as e:
            self.logger.info(f"没有login.srf页面，继续执行: {str(e)}")
            
        # 处理多重验证
        try:
            await page.wait_for_url('https://account.live.com/proofs/**', timeout=3000)
            await page.fill("#EmailAddress", proof_email)
            timestamp = int(time.time())
            await page.click('input[type="submit"]')
            # 获取验证码
            verification_code = await MailVerificationHelper.get_ms_verification_code(
                proof_api,
                proof_email,
                timestamp
            )
            await page.fill('input[type="tel"]', verification_code)
            await page.click('input[type="submit"]')
        except Exception as e:
            self.logger.info(f"没有处理多重验证，继续执行: {str(e)}")
       
    async def handler_single_account(self, page: Page, email: str, password: str, index: int = 0, total: int = 0, max_retries: int = 4):
        """执行登录操作"""
        retry_count = 0
        while retry_count < max_retries:
            try:
                self.logger.info(f"正在执行第 {index}/{total} 个账号 - 邮箱: {email} 密码: {password} (第 {retry_count + 1} 次尝试)")
                
                # 清除所有cookies
                await page.context.clear_cookies()
                
                # 随机选择一个 proof_email 配置并初始化 verification_helper
                proof_email_config = self.config_manager.proof_email[0]
                proof_domain = proof_email_config['domain']
                proof_api = proof_email_config['api']
                
                # 输入邮箱
                await page.goto("https://rewards.bing.com")
                await page.fill('input[type="email"]', email)
                await page.click('button[type="submit"]')
                
                # 输入密码
                await page.wait_for_url('https://login.live.com/**', timeout=3000)
                await page.fill('input[type="password"]', password)
                await page.click('button[type="submit"]')
                
                # 密码错误,可能已经修改过了
                try:
                    await page.wait_for_url('https://login.live.com/ppsecure/**', timeout=3000)
                    #await page.wait_for_selector("#i0118Error",timeout=3000)
                    await page.wait_for_selector("#field-8__validationMessage",timeout=5000)
                    break
                except Exception as e:
                    self.logger.info(f"密码未被修改，继续执行: {str(e)}")
    
                # 等待手机验证
                service_abuseLanding = False
                try:
                    await page.wait_for_url(lambda url: url.startswith('https://account.live.com/Abuse'), timeout=5000)
                    service_abuseLanding = True
                except Exception as e:
                    self.logger.info(f"没有手机验证页面，继续执行: {str(e)}")
    
                if service_abuseLanding:
                    retry_count = max_retries
                    raise Exception("账号被锁定")
                
                # 修改 proof_email 的生成方式
                proof_email = f"{email.split('@')[0].lower()}@{proof_domain}"
                
                
                # 处理多重验证
                # try:
                #     await page.wait_for_url('https://account.live.com/proofs/**', timeout=3000)
                #     await page.fill("#EmailAddress", proof_email)
                #     timestamp = int(time.time())
                #     await page.click('input[type="submit"]')
                #     # 获取验证码
                #     verification_code = await MailVerificationHelper.get_ms_verification_code(
                #         proof_api,
                #         proof_email,
                #         timestamp
                #     )
                #     await page.fill('input[type="tel"]', verification_code)
                #     await page.click('input[type="submit"]')
                # except Exception as e:
                #     self.logger.info(f"没有直接输入email验证，继续执行: {str(e)}")
                
                # 处理多重验证
                try:
                    await page.wait_for_url('https://account.live.com/proofs/**', timeout=3000)
                    
                    hasOption = False
                    try:
                        await page.wait_for_selector("#iProof0", timeout=1000)
                        hasOption = True
                    except Exception as e:
                        self.logger.info(f"没有选择email: {str(e)}")
                        
                    
                    timestamp = int(time.time())
                    if hasOption:
                        await page.click('#iProof0')
                        await page.click('input[type="submit"]')
                    else:
                        await page.fill("#EmailAddress", proof_email) 
                        await page.click('input[type="submit"]')

                    # 获取验证码
                    verification_code = await MailVerificationHelper.get_ms_verification_code(
                        proof_api,
                        proof_email,
                        timestamp
                    )
                    await page.fill('input[type="tel"]', verification_code)
                    await page.click('input[type="submit"]')
                except Exception as e:
                    self.logger.info(f"没有处理多重验证，继续执行: {str(e)}")
                

                # 处理多重验证2
                for i in range(2):
                    try:
                        await page.wait_for_url('https://account.live.com/identity/**', timeout=3000)
                        await page.click('#iProof0')
                        
                        # 使用选择器定位到这个 radio 输入元素
                        radio_element = page.locator('input#iProof0[type="radio"][name="proof"]')
                        
                        # 获取 value 属性值
                        value = await radio_element.get_attribute("value")
                        if(value.find(proof_domain) == -1):
                            retry_count = max_retries
                            raise Exception("账号已经被绑定到其他邮箱")
                        
                        await page.fill('#iProofEmail', proof_email)
                        timestamp = int(time.time())
                        await page.click('#iSelectProofAction')

                        # 获取验证码
                        verification_code = await MailVerificationHelper.get_ms_verification_code(
                            proof_api,
                            proof_email,
                            timestamp
                        )
                        await page.fill('input[type="tel"]', verification_code)
                        await page.click('input[type="submit"]')
                    except PlaywrightTimeoutError as e:
                        self.logger.info(f"没有选择邮箱认证，继续执行: {str(e)}")

                # 处理隐私通知
                # try:
                #     await page.wait_for_url('https://privacynotice.account.microsoft.com/**', timeout=5000)
                #     await page.click('#id__0')
                # except Exception as e:
                #     self.logger.info(f"没有隐私通知页面，继续执行: {str(e)}")
    
                # 等待登录成功
                await page.wait_for_url('https://login.live.com/**', timeout=5000)
                await page.click('button[type="submit"]')
                # 跳转到主页修改密码
                # await page.goto("https://account.microsoft.com/",timeout=60000)

                # # 处理安全通知
                # try:
                #     await page.wait_for_url('https://account.microsoft.com/account-checkup**', timeout=30000)
                #     await page.click('#landing-page-dialog\\.close')
                # except Exception as e:
                #     print(f"没有安全通知页面，继续执行: {str(e)}")

 
                # await page.wait_for_selector("#main-content-landing-react",timeout=120000)
                # await page.click('#home\\.drawers\\.security')
                # await page.click('[data-bi-id="home.drawers.security.change-password"]')
                # await self.loginsrf(page,proof_api,proof_email)
                await asyncio.sleep(3)
                self.logger.info(f"开始跳转到密码修改页面")
                await page.goto("https://account.live.com/password/Change",timeout=60000)
               
                
                await self.loginsrf(page,proof_api,proof_email)
                
                # 处理隐私通知
                try:
                    await page.wait_for_url('https://privacynotice.account.microsoft.com/**', timeout=10000)
                    await page.click('#id__0')
                except Exception as e:
                    self.logger.info(f"没有隐私通知页面，继续执行: {str(e)}")
    
                try:
                    await page.wait_for_url('https://account.live.com/password/**', wait_until='load', timeout=60000)
                    #await page.fill('#iCurPassword', password)
                    new_passwd = self.config_manager.user_new_password
                    await page.fill('#iPassword', new_passwd)
                    await page.fill('#iRetypePassword', new_passwd)
                    await page.click('#UpdatePasswordAction')
                    
                    await page.wait_for_timeout(5000)
                except Exception as e:
                    raise Exception(f"Failed to change password: {str(e)}")
            
                # 等待登录成功
                self.logger.info(f'登录修改密码和绑定邮箱成功 for {email}')
                break
            except EmailError as e:
                break
            except Exception as e:
                retry_count += 1
                self.logger.error(f"Login attempt {retry_count} failed for {email}: {str(e)}")
                
                if retry_count >= max_retries:
                    self.logger.error(f"Max retries reached for {email}, moving to next account")
                    raise Exception(f"Failed after {max_retries} attempts: {str(e)}")
                
                # 等待一段时间后重试
                await asyncio.sleep(random.uniform(5, 10))
                continue

    async def do(self, playwright: Playwright, accounts: list, max_concurrent_tasks: int = 1):
        failed_accounts = []
        success_accounts = []  # 新增成功账号列表
        
        sem = asyncio.Semaphore(max_concurrent_tasks)
        
        tasks = []
        # 获取当前事件循环
        loop = asyncio.get_running_loop()

        # 定义信号处理函数
        def handle_sigint():
            """处理Ctrl+C信号"""
            self.logger.info("\nReceived termination signal, cancelling tasks...")
            for task in tasks:
                if not task.done():
                    task.cancel()

        # 注册信号处理器（兼容Windows）
        if sys.platform == 'win32':
            # Windows使用默认信号处理
            signal.signal(signal.SIGINT, lambda s, f: loop.call_soon_threadsafe(handle_sigint))
        else:
            # Unix系统使用add_signal_handler
            loop.add_signal_handler(signal.SIGINT, handle_sigint)
        
        # 定义具有信号量的处理任务函数
        async def process_account_group(account_group):
            async with sem:
                return await self.batch(playwright, account_group)
        
        # 假设max_concurrent_tasks代表组数
        accounts_per_group = len(accounts) // max_concurrent_tasks
        remainder = len(accounts) % max_concurrent_tasks

        account_groups = []
        start_idx = 0

        for i in range(max_concurrent_tasks):
            # 如果有余数，前remainder组多分配一个账号
            group_size = accounts_per_group + (1 if i < remainder else 0)
            account_groups.append(accounts[start_idx:start_idx + group_size])
            start_idx += group_size
        
        # 创建并跟踪所有任务
        for account_group in account_groups:
            task = asyncio.create_task(process_account_group(account_group))
            tasks.append(task)
    
        try:
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 聚合异常结果
            for result in results:
                if isinstance(result, Exception):
                    self.logger.error(f"Task failed: {str(result)}")
                elif isinstance(result, tuple):  # 修改为元组类型检查
                    success_list, failed_list = result  # 解包元组
                    success_accounts.extend(success_list)
                    failed_accounts.extend(failed_list)
                    
        except asyncio.CancelledError:
            self.logger.info("Main execution was cancelled")
        finally:
            # 清理信号处理器
            if sys.platform != 'win32':
                loop.remove_signal_handler(signal.SIGINT)
    
        return success_accounts, failed_accounts  # 修改返回值


    async def batch(self, playwright: Playwright, accounts: list):
        """批量处理奖励任务"""
        failed_accounts = []
        success_accounts = []  # 新增成功账号列表
        total = len(accounts)
        # Configure proxy settings if provided
        browser_options = {
            'headless': False,
            'args': ['--disable-web-security']
        }
        
        context_options = {
            'ignore_https_errors': True
        }
        
        # Get available proxies from config
        proxies = self.config_manager.get('proxies', [])
        if proxies:
            proxy = random.choice(proxies)
            if proxy.get('host') and proxy['host'].strip() != "":
                proxy_config = {
                    'server': proxy['host'],
                }
                
                # Add authentication if provided
                if proxy.get('username') and proxy.get('password'):
                    proxy_config['username'] = proxy['username']
                    proxy_config['password'] = proxy['password']
                
                context_options['proxy'] = proxy_config
                self.logger.info(f"Using proxy: {proxy['host']}")        
        # Launch browser and create context
        browser = await playwright.chromium.launch(**browser_options)
        context = await browser.new_context(**context_options)
        page = await context.new_page()
        
        try:
            for index, account in enumerate(accounts, 1):
                try:
                    email, password = account.strip().split(':')
                    await self.handler_single_account(page, email, password, index, total)
                    # 成功后等待一段随机时间，避免操作过于频繁
                    await asyncio.sleep(random.uniform(3, 5))
                    success_accounts.append(account)  # 添加成功的账号
                except Exception as e:
                    logging.error(f"第 {index}/{total} 个账号处理失败 {email}: {str(e)}")
                    failed_accounts.append(account)
        finally:
            await page.close()
            await context.close()
            await browser.close()
            
        return success_accounts, failed_accounts  # 修改返回值

