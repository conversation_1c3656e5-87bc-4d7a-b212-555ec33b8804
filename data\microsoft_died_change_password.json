[{"email": "su<PERSON><PERSON><EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "su<PERSON><PERSON><EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>"}, {"id": 100, "email": "<EMAIL>", "password": "Dean0104@microsoft", "proofEmail": "<EMAIL>", "server": "alibj1", "score": 0, "weight": 0, "disabled": 0, "lock": 0, "pcSearchPointProgress": 0, "mobileSearchPointProgress": 0, "pcSearchCount": 0, "mobileSearchCount": 0, "executions": 0, "updateDatetime": ""}]